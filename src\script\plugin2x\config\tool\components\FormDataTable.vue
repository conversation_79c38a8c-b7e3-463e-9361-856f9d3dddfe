<template>
    <div class="form-table" :class="{ 'out-border': outBorder }">
        <!-- 表头 -->
        <div class="form-table__header" :style="gridStyle">
            <div
                class="form-table__cell form-table__cell-header"
                v-for="(col, index) in columns"
                :key="`head-` + index"
            >
                {{ col.title }}
            </div>
            <div class="form-table__cell form-table__cell-header op-column">操作</div>
        </div>
        <!-- 数据行 -->
        <div class="form-table__body custom-scrollbar">
            <div
                class="form-table__row"
                v-for="(row, rowIndex) in modelValue"
                :key="rowIndex"
                :style="gridStyle"
            >
                <div
                    class="form-table__cell form-table__cell-body"
                    v-for="(col, colIndex) in columns"
                    :key="colIndex"
                >
                    <template>
                        <el-input
                            v-if="col.type === 'input'"
                            v-model="row[col.prop]"
                            size="small"
                            clearable
                            :placeholder="col.placeholder || '请输入'"
                        />
                        <el-select
                            v-else-if="col.type === 'select'"
                            v-model="row[col.prop]"
                            size="small"
                            clearable
                            placeholder="请选择"
                            class="w-100"
                            popper-class="mcpservice-theme"
                        >
                            <el-option
                                v-for="(option, optionIndex) in col.options"
                                :key="optionIndex"
                                :label="option.label"
                                :value="option.value"
                            />
                        </el-select>
                    </template>
                </div>
                <div class="form-table__cell form-table__cell-body op-column">
                    <span
                        class="op-column-text trigger-btn"
                        :class="{ active: row.isValid }"
                        @click="triggerRow(rowIndex)"
                        >{{ row.isValid ? '起效' : '不起效' }}
                    </span>
                    <span class="op-column-text delete-btn" @click="deleteRow(rowIndex)">删除</span>
                </div>
            </div>
        </div>
        <div class="form-table__footer">
            <el-button type="plain" icon="el-icon-plus" class="add-row-btn" @click="addRow"
                >新增
            </el-button>
        </div>
    </div>
</template>

<script>
export default {
    name: 'FormDataTable',
    model: {
        prop: 'modelValue',
        event: 'update:modelValue'
    },
    props: {
        // v-model绑定值
        modelValue: {
            type: Array,
            default: () => []
        },
        // 表格列配置
        columns: {
            type: Array,
            default: () => []
        },
        // 默认行数据
        defaultRowData: {
            type: Object,
            default: () => ({})
        },
        outBorder: {
            type: Boolean,
            default: true
        }
    },
    created() {
        // 非只读且初始为空时，自动添加一行空数据
        if (!this.modelValue || this.modelValue.length === 0) {
            const initRow = { ...this.defaultRowData, isValid: 1 };
            this.$emit('update:modelValue', [initRow]);
        }
    },
    methods: {
        // 添加新行
        addRow() {
            const newRow = { ...this.defaultRowData, isValid: 1 };
            const newData = [...this.modelValue, newRow];
            this.$emit('update:modelValue', newData);
        },
        // 删除行
        deleteRow(index) {
            const newData = [...this.modelValue];
            newData.splice(index, 1);
            this.$emit('update:modelValue', newData);
        },
        // 切换行是否起效
        triggerRow(index) {
            const newData = [...this.modelValue];
            newData[index] = {
                ...newData[index],
                isValid: +!newData[index].isValid
            };
            this.$emit('update:modelValue', newData);
        }
    },
    computed: {
        gridTemplateColumns() {
            if (!Array.isArray(this.columns) || this.columns.length === 0) return '';
            const arr = this.columns.map((col) => {
                if (col && col.width !== undefined) {
                    if (typeof col.width === 'number') {
                        return col.width + 'px';
                    }
                    return col.width;
                }
                return '1fr';
            });
            // 操作列固定宽度
            arr.push('150px');
            return arr.join(' ');
        },
        gridStyle() {
            return { gridTemplateColumns: this.gridTemplateColumns };
        }
    }
};
</script>

<style lang="less" scoped>
.form-table {
    display: flex;
    flex-direction: column;
    width: 100%;
    overflow: hidden;
    &.out-border {
        border: 1px solid #d6dae0;
        border-radius: 4px;
    }

    .form-table__header,
    .form-table__row {
        display: grid;
        align-items: center;
    }

    .form-table__header {
        background: #f5f7fa;
        font-weight: 500;
        color: #606266;
        border-bottom: 1px solid #d6dae0;
    }

    .form-table__cell {
        display: flex;
        align-items: center;
        &.form-table__cell-header {
            height: 40px;
            padding: 0 25px;
            border: none;
            font-family:
                PingFangSC,
                PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);
            &:first-child {
                padding-left: 34px;
            }
            &:last-child {
                padding-right: 24px;
            }
            &.op-column {
                gap: 8px;
                padding-left: 15px;
            }
        }
        &.form-table__cell-body {
            height: 48px;
            padding: 0 15px;
            border-bottom: 1px solid #d6dae0;
            &:first-child {
                padding-left: 24px;
            }
            &:last-child {
                padding-right: 24px;
            }
            &.op-column {
                gap: 8px;
                padding-left: 15px;
            }
        }
    }
    .op-column-text {
        display: flex;
        align-items: center;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        cursor: pointer;
        user-select: none;
        &.trigger-btn {
            width: 60%;
        }
        &.active {
            color: #1565ff;
        }
        &.delete-btn {
            width: 40%;
            color: #ff4d4f;
            position: relative;
            &::before {
                content: '';
                position: absolute;
                left: -12px;
                top: 50%;
                transform: translateY(-50%);
                width: 1px;
                height: 60%;
                background: #d6dae0;
            }
        }
    }

    .form-table__body {
        width: 100%;
        min-height: 0;
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow-y: auto;
    }

    .form-table__footer {
        width: 100%;
        height: 48px;
        padding-left: 24px;
        display: flex;
        align-items: center;
        align-self: flex-start;
        border-top: 1px solid #d6dae0;
        .add-row-btn {
            margin: 0;
            background: #ffffff;
            border-radius: 4px;
            border: 1px solid #1565ff99;
            color: #1565ff;
            &:hover {
                background: #1565ff1a;
            }
        }
    }

    // 统一 Element UI 输入高度
    /deep/ .el-input__inner,
    /deep/ .el-select {
        height: 32px;
        line-height: 32px;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
    }
}
</style>
